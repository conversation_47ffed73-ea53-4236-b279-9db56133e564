import { Injectable } from '@nestjs/common';
import { Op } from 'sequelize';
import { EmiEntity } from 'src/database/pg/entities/emi.entity';
import { loanTransaction } from 'src/database/pg/entities/loanTransaction';
import { PgService } from 'src/database/pg/pg.service';
import { DateService } from 'src/utils/date.service';

@Injectable()
export class AnalyticsService {
  constructor(
    private readonly pg: PgService,
    private readonly dateService: DateService,
  ) {}

  //#region funPaidEmiAnalytics
  async funPaidEmiAnalytics(query) {
    let month = query?.month;
    let startDate = new Date();
    startDate.setDate(1);
    let endDate: Date;
    if (month || month == 0) {
      if (month > startDate.getMonth()) {
        startDate.setFullYear(startDate.getFullYear() - 1);
      }
      startDate.setMonth(month);
    }
    endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0);
    startDate = this.dateService.getGlobalDate(startDate);
    endDate = this.dateService.getGlobalDate(endDate);

    const allEmiData = await this.pg.findAll(EmiEntity, {
      attributes: ['loanId', 'emi_date', 'payment_done_date', 'id'],
      where: {
        emi_date: { [Op.gte]: startDate, [Op.lt]: endDate },
        payment_done_date: { [Op.ne]: null },
        payment_status: '1',
      },
      raw: true,
    });

    // Collect all  loanIds
    const loanIds = [
      ...new Set(allEmiData.map((e) => e.loanId).filter(Boolean)),
    ];
    if (loanIds.length === 0) {
      return this.generateAprBins([], 0, 'emi');
    }

    const allLoans = await this.pg.findAll(loanTransaction, {
      attributes: ['id', 'esign_id'],
      where: { id: { [Op.in]: loanIds } },
      raw: true,
    });
    const loanMap = new Map(allLoans.map((l) => [l.id, l]));

    const esignIds = [
      ...new Set(allLoans.map((l) => l.esign_id).filter(Boolean)),
    ];
    if (esignIds.length === 0) {
      return this.generateAprBins([], 0, 'emi');
    }

    const allEsigns = await this.pg.findAll('esignEntity', {
      attributes: ['id', 'eSign_agree_data'],
      where: { id: { [Op.in]: esignIds } },
      raw: true,
    });
    const esignMap = new Map(allEsigns.map((e) => [e.id, e]));

    const allAprs: number[] = [];
    let totalPaidEmisCount = 0;
    for (const emi of allEmiData) {
      const loan = loanMap.get(emi.loanId);
      if (!loan?.esign_id) continue;
      const esign = esignMap.get(loan.esign_id);
      if (!esign?.eSign_agree_data) continue;
      let apr = null;
      try {
        const data = JSON.parse(esign.eSign_agree_data);
        apr = data.aprCharges ? +data.aprCharges : null;
      } catch {
        apr = null;
      }
      if (apr) {
        allAprs.push(apr);
        totalPaidEmisCount++;
      }
    }
    return this.generateAprBins(allAprs, totalPaidEmisCount, 'emi');
  }
  //#endregion

  //#region funDisbursedLoanCount
  async funDisbursedLoanAnalytics(query) {
    let month = query?.month;
    let startDate = new Date();
    startDate.setDate(1);
    let endDate: Date;
    if (month || month == 0) {
      if (month > startDate.getMonth()) {
        startDate.setFullYear(startDate.getFullYear() - 1);
      }
      startDate.setMonth(month);
    }
    endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0);
    startDate = this.dateService.getGlobalDate(startDate);
    endDate = this.dateService.getGlobalDate(endDate);

    const loanData = await this.pg.findAll(loanTransaction, {
      attributes: ['id', 'esign_id'],
      where: {
        loan_disbursement_date: {
          [Op.gte]: startDate.toISOString(),
          [Op.lte]: endDate.toISOString(),
        },
        loanStatus: { [Op.in]: ['Active', 'Complete'] },
      },
      raw: true,
    });

    const allAprs: number[] = [];
    let totalLoansCount = 0;

    for (const loan of loanData) {
      if (!loan.id) continue;
      const esign = await this.pg.findOne('esignEntity', {
        attributes: ['eSign_agree_data'],
        where: { id: loan.esign_id },
        raw: true,
      });
      const apr = (() => {
        if (!esign?.eSign_agree_data) return null;
        try {
          const data = JSON.parse(esign.eSign_agree_data);
          return data.aprCharges ? +data.aprCharges : null;
        } catch {
          return null;
        }
      })();
      if (apr) {
        allAprs.push(apr);
        totalLoansCount++;
      }
    }
    return this.generateAprBins(allAprs, totalLoansCount, 'loan');
  }
  //#endregion

  //#region generateAprBins
  private generateAprBins(
    aprValues: number[],
    totalCount: number = 0,
    type: 'emi' | 'loan' = 'emi',
  ) {
    if (!aprValues?.length || totalCount === 0) {
      return { total: 0 };
    }
    const binSize = 10;
    const minApr = Math.min(...aprValues);
    const maxApr = Math.max(...aprValues);
    const startBin = Math.floor(minApr / binSize) * binSize;
    const endBin = Math.ceil(maxApr / binSize) * binSize;
    const binData = {};

    for (let i = startBin; i < endBin; i += binSize) {
      const binMax = i + binSize - 1;
      const binKey = `${i}-${binMax}`;

      const binCount = aprValues.filter(
        (apr) => apr >= i && apr <= binMax,
      ).length;

      if (binCount > 0) {
        const percentage = +((binCount / totalCount) * 100).toFixed(2);
        const fieldName = type === 'loan' ? 'totalLoans' : 'totalEmi';
        binData[binKey] = { percentage, [fieldName]: binCount };
      }
    }

    const result = {
      ...binData,
      total: totalCount,
    };
    return result;
  }
  //#endregion
}
