// Imports
import {
  Table,
  Model,
  Column,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { loanTransaction } from './loanTransaction';

@Table({})
export class esignEntity extends Model<esignEntity> {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    allowNull: false,
  })
  id: number;

  @ForeignKey(() => loanTransaction)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;

  @BelongsTo(() => loanTransaction)
  loan: loanTransaction;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  eSign_agree_data: string;
}
