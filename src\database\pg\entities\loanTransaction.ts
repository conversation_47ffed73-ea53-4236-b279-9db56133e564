// Imports
import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
  AfterFind,
} from 'sequelize-typescript';
import { TEXT } from 'sequelize';
import { admin } from './admin.entity';
import { decryptPhone } from 'src/utils/crypt';
import { BankingEntity } from './banking.entity';
import { registeredUsers } from './registeredUsers';
import { PredictionEntity } from './prediction.entity';
import { CibilScoreEntity } from './cibil.score.entity';
import { esignEntity } from './esign.entity';

@Table({})
export class loanTransaction extends Model<loanTransaction> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ForeignKey(() => registeredUsers)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  userId: string;

  @BelongsTo(() => registeredUsers, {
    foreignKey: 'userId',
    targetKey: 'id',
    constraints: false,
  })
  registeredUsers: registeredUsers;

  @ForeignKey(() => BankingEntity)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  bankingId: number;

  @BelongsTo(() => BankingEntity)
  bankingData: BankingEntity;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  manualVerificationAcceptId: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: 'Loan approved amount',
  })
  netApprovedAmount: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  interestRate: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  approvedDuration: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  loan_disbursement_date: string;

  @Column({
    type: DataType.DOUBLE,
    allowNull: true,
  })
  loanFees: number;

  @ForeignKey(() => esignEntity)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  esign_id: number;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
    defaultValue: {
      planAPremium: 0,
      planBPremium: 0,
      totalPremium: 0,
      planBSumInsured: 0,
      status: 'yet_to_processed',
    },
  })
  insuranceDetails: any;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
    defaultValue: {
      gst_per: 0,
      gst_amt: 0,
      sgst_amt: 0,
      cgst_amt: 0,
      doc_charge_per: 0,
      insurance_fee: 0,
      risk_assessment_per: 0,
      risk_assessment_charge: 0,
    },
  })
  charges: {
    gst_per: number;
    gst_amt: number;
    sgst_amt: number;
    cgst_amt: number;
    doc_charge_per: number;
    doc_charge_amt: number;
    insurance_fee: number;
    risk_assessment_per: number;
    risk_assessment_charge: number;
  };

  @Column({
    type: DataType.ENUM,
    values: ['0', '1', '2'],
    comment: '0=Android 1=iOS 2=Web',
    allowNull: true,
  })
  finalTypeOfDevice: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: null,
  })
  completedLoan: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  loanCompletionDate: string;

  @Column({
    type: DataType.ARRAY(TEXT),
    allowNull: true,
  })
  netEmiData: any;

  @Column({
    type: DataType.DOUBLE,
    allowNull: true,
  })
  loanGmv: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  companyId: number;

  @Column({
    type: DataType.ENUM,
    defaultValue: 'InProcess',
    values: ['InProcess', 'Accepted', 'Active', 'Rejected', 'Complete'],
    allowNull: false,
  })
  loanStatus: string;

  @ForeignKey(() => CibilScoreEntity)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  cibilId: number;

  @BelongsTo(() => CibilScoreEntity, {
    foreignKey: 'cibilId',
    targetKey: 'id',
    constraints: false,
  })
  cibilData: CibilScoreEntity;

  @Column({
    type: DataType.ENUM,
    values: ['0', '1', '2'],
    comment: '0=Android 1=iOS 2=Web',
    allowNull: true,
  })
  initialTypeOfDevice: string;

  @ForeignKey(() => admin)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  followerId: number;

  @ForeignKey(() => PredictionEntity)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  predictionId: number;

  @BelongsTo(() => PredictionEntity)
  predictionData: PredictionEntity;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    comment: 'loan disbursement date with time',
  })
  loanDisbursementDateTime: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    comment: 'loan reject date with time',
  })
  loanRejectDateTime: Date;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  fullName: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  email: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  phone: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  hashPhone: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  assignTo: number;

  @Column({ type: DataType.STRING, allowNull: true })
  Loss_assets: string;

  @AfterFind
  static formatAfterFind(instances: loanTransaction[] | loanTransaction | any) {
    // If multiple records are found (findAll)
    if (Array.isArray(instances)) {
      if (instances.length > 0) {
        if (
          instances[0].interestRate ||
          instances[0].netApprovedAmount ||
          instances[0].approvedDuration ||
          instances[0].phone
        ) {
          instances.forEach((instance: any) => {
            if (instance.interestRate) {
              instance.interestRate = +instance.interestRate;
            }
            if (instance.netApprovedAmount) {
              instance.netApprovedAmount = +instance.netApprovedAmount;
            }
            if (instance.approvedDuration) {
              instance.approvedDuration = +instance.approvedDuration;
            }
            if (instance.phone) {
              instance.phone = decryptPhone(instance.phone);
            }
          });
        }
      }
    } // Single record (findOne)
    else if (instances) {
      if (
        instances.interestRate ||
        instances.netApprovedAmount ||
        instances.approvedDuration
      ) {
        if (instances.interestRate) {
          instances.interestRate = +instances.interestRate;
        }
        if (instances.netApprovedAmount) {
          instances.netApprovedAmount = +instances.netApprovedAmount;
        }
        if (instances.approvedDuration) {
          instances.approvedDuration = +instances.approvedDuration;
        }
        if (instances.phone) {
          instances.phone = decryptPhone(instances.phone);
        }
      }
    }
  }
}
